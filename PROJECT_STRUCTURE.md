# Bot Review Assistant - Project Structure

## 📁 New Clean Architecture

```
bot-assistance/
├── src/                              # Source code
│   └── bot_review/                   # Main package
│       ├── __init__.py              # Package exports
│       ├── agents/                   # Specialized review agents
│       │   ├── __init__.py
│       │   ├── base.py              # BaseAgent class + LLM management
│       │   ├── naming.py            # NamingAgent - naming conventions
│       │   ├── syntax.py            # SyntaxAgent - syntax validation
│       │   ├── logic.py             # LogicAgent - logic analysis
│       │   └── summary.py           # SummaryAgent - feedback synthesis
│       ├── pipeline/                 # Workflow orchestration
│       │   ├── __init__.py
│       │   ├── nodes.py             # LangGraph node functions
│       │   └── workflow.py          # Main ReviewPipeline class
│       ├── core/                     # Core utilities
│       │   ├── __init__.py
│       │   ├── tools.py             # Code analysis tools
│       │   └── vector_store.py      # Vector operations
│       └── config/                   # Configuration
│           ├── __init__.py
│           └── prompts.py           # System prompts
├── models/                           # Model files only (no __init__.py)
│   ├── DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf
│   └── all-MiniLM-L6-v2-Q4_K_M.gguf
├── tests/                            # Test suite
│   ├── __init__.py
│   └── test_agents.py               # Agent and pipeline tests
├── examples/                         # Usage examples
│   ├── __init__.py
│   └── example_usage.py             # Example script
├── app.py                           # Streamlit web interface
├── main.py                          # Alternative entry point
├── requirements.txt                 # Dependencies
├── README.md                        # Project documentation
├── PROJECT_STRUCTURE.md             # This file
├── AGENT_REFACTOR_NOTES.md          # Refactor documentation
└── __init__.py                      # Root package marker
```

## 🎯 **Key Improvements**

### ✅ **Clear Separation of Concerns**
- **Agents**: Specialized AI agents with focused responsibilities
- **Pipeline**: Workflow orchestration and state management
- **Core**: Reusable utilities and tools
- **Config**: Centralized configuration management

### ✅ **Better Module Organization**
- Each agent in its own file with specialized system prompt
- Pipeline components separated from business logic
- Core utilities isolated and reusable
- Clean import structure

### ✅ **Improved Entry Points**
- `app.py`: Main Streamlit web interface
- `main.py`: Alternative entry point (backward compatibility)
- `examples/`: Usage examples and demos
- `tests/`: Comprehensive test suite

### ✅ **Cleaner Dependencies**
- Models folder contains only model files
- No circular imports
- Clear dependency hierarchy
- Proper Python package structure

## 🚀 **Usage**

### Web Interface
```bash
streamlit run app.py
```

### Programmatic Usage
```python
from src.bot_review import ReviewPipeline

pipeline = ReviewPipeline()
result = pipeline.run(diff_text)
```

### Individual Agents
```python
from src.bot_review.agents import NamingAgent, SyntaxAgent

naming_agent = NamingAgent()
syntax_agent = SyntaxAgent()

naming_result = naming_agent.process(declarations)
syntax_result = syntax_agent.process(code, language)
```

## 🧪 **Testing**
```bash
python tests/test_agents.py
```

## 📦 **Package Structure Benefits**

### **Maintainability**
- Each component has a single responsibility
- Easy to locate and modify specific functionality
- Clear dependency relationships

### **Scalability**
- Easy to add new agents
- Simple to extend pipeline with new nodes
- Modular architecture supports growth

### **Testability**
- Individual components can be tested in isolation
- Clear interfaces between modules
- Comprehensive test coverage possible

### **Reusability**
- Agents can be used independently
- Core utilities are shared across components
- Pipeline can be embedded in other applications

## 🔄 **Migration from Old Structure**

### **What Changed**
- `code_review_agent/` → `src/bot_review/`
- `utils/` split into `agents/`, `core/`, `pipeline/`, `config/`
- Individual agent files instead of monolithic `agents.py`
- Cleaner import paths and dependencies

### **What Stayed the Same**
- Public API remains compatible
- Same functionality and features
- Model files and requirements unchanged
- Streamlit interface unchanged

## 🎨 **Design Principles**

1. **Single Responsibility**: Each module has one clear purpose
2. **Dependency Inversion**: High-level modules don't depend on low-level details
3. **Open/Closed**: Open for extension, closed for modification
4. **Interface Segregation**: Clean, focused interfaces
5. **DRY**: Don't repeat yourself - shared utilities in core/

This new structure provides a solid foundation for future development and makes the codebase much more professional and maintainable.
