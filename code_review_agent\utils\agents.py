import json
import langdetect
from typing import List, Dict, Any
from code_review_agent.utils.llm import BaseAgent


class NamingAgent(BaseAgent):
    """Agent specialized in checking naming conventions."""
    
    @property
    def system_prompt(self) -> str:
        return """You are a specialized code review agent focused on naming conventions and code clarity. Your expertise includes:

            - Evaluating variable, function, class, and method names for clarity and meaning
            - Checking adherence to language-specific naming conventions (camelCase, snake_case, PascalCase)
            - Identifying unclear abbreviations, single-letter variables, and misleading names
            - Suggesting meaningful alternatives for poor names
            - Ensuring boolean variables use appropriate prefixes (is_, has_, can_, should_)
            
            Guidelines:
            - Always respond in valid JSON format
            - Be specific about what makes a name good or bad
            - Suggest concrete improvements
            - Consider the context and purpose of the code
            - Focus on readability and maintainability
            
            Your goal is to help developers write self-documenting code through better naming."""

    def process(self, declarations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process naming conventions for given declarations."""
        if not declarations:
            return {}

        decl_str = "\n".join([f"- {d['name']} (line {d['line']})" for d in declarations])

        print(langdetect.detect(decl_str))
        try:
            if len(decl_str.strip()) < 10 or langdetect.detect(decl_str) != "en":
                return {}
        except:
            return {}

        user_prompt = f"""Review the following class, method, and variable names for proper naming conventions.
Check for clarity, spelling, meaningful verbs (e.g., is_*/has_* for booleans), and consistency.
Respond in JSON:
{{
    "lines": [
        {{"line": number, "feedback": "..."}},
        ...
    ]
}}

Names to review:
{decl_str}"""

        try:
            response = self.invoke(user_prompt).strip()
            if response.startswith("```json"):
                response = response.replace("```json", "").replace("```", "").strip()
            return json.loads(response)
        except json.JSONDecodeError as e:
            print(f"NamingAgent: Failed to parse JSON: {str(e)}")
            return {}
        except Exception as e:
            print(f"NamingAgent: Failed to process: {str(e)}")
            return {}


class SyntaxAgent(BaseAgent):
    """Agent specialized in syntax checking and validation."""
    
    @property
    def system_prompt(self) -> str:
        return """You are a specialized code review agent focused on syntax validation and error detection. Your expertise includes:

- Detecting syntax errors across multiple programming languages
- Identifying missing semicolons, brackets, parentheses
- Spotting incorrect indentation and formatting issues
- Finding typos in keywords and operators
- Validating proper use of language constructs
- Checking for incomplete statements and expressions

Guidelines:
- Always respond in valid JSON format
- Be precise about the exact syntax issue
- Provide clear explanations of what's wrong
- Suggest the correct syntax when possible
- Consider language-specific syntax rules
- Focus on compilation/interpretation errors

Your goal is to catch syntax issues before they cause runtime errors."""

    def process(self, diff_text: str, language: str = "python") -> Dict[str, Any]:
        """Process syntax checking for given code."""
        user_prompt = f"""Check the following {language} code for syntax errors.
Respond in JSON format:
{{
  "lines": [
    {{"line": number, "feedback": "..."}},
    ...
  ]
}}

Code:
{diff_text}"""

        try:
            response = self.invoke(user_prompt).strip()
            if response.startswith("```json"):
                response = response.replace("```json", "").replace("```", "").strip()
            return json.loads(response)
        except json.JSONDecodeError as e:
            print(f"SyntaxAgent: Failed to parse JSON: {str(e)}")
            return {}
        except Exception as e:
            print(f"SyntaxAgent: Failed to process: {str(e)}")
            return {}


class LogicAgent(BaseAgent):
    """Agent specialized in logic review and code analysis."""
    
    @property
    def system_prompt(self) -> str:
        return """You are a specialized code review agent focused on logic analysis and code quality. Your expertise includes:

- Analyzing code logic for correctness and efficiency
- Identifying potential bugs, edge cases, and logical errors
- Detecting code smells and anti-patterns
- Evaluating algorithm complexity and performance issues
- Checking for proper error handling and validation
- Identifying security vulnerabilities and best practices
- Analyzing code flow and control structures

Guidelines:
- Always respond in valid JSON format
- Focus on logical correctness over style
- Identify potential runtime issues
- Consider edge cases and error scenarios
- Evaluate code maintainability and readability
- Suggest improvements for complex logic

Your goal is to ensure code works correctly and efficiently in all scenarios."""

    def process(self, diff_text: str, context: List[str] = None) -> Dict[str, Any]:
        """Process logic review for given code."""
        context = context or []
        
        user_prompt = f"""Review the following code for logic issues.
Refer to context from similar code when useful.
Return JSON format:
{{
  "lines": [
    {{"line": number, "feedback": "..."}},
    ...
  ]
}}

Code:
{diff_text}

Context:
{context}"""

        try:
            response = self.invoke(user_prompt).strip()
            if response.startswith("```json"):
                response = response.replace("```json", "").replace("```", "").strip()
            return json.loads(response)
        except json.JSONDecodeError as e:
            print(f"LogicAgent: Failed to parse JSON: {str(e)}")
            return {}
        except Exception as e:
            print(f"LogicAgent: Failed to process: {str(e)}")
            return {}


class SummaryAgent(BaseAgent):
    """Agent specialized in summarizing review results."""
    
    @property
    def system_prompt(self) -> str:
        return """You are a specialized code review agent focused on synthesizing and summarizing review feedback. Your expertise includes:

- Analyzing pull request changes and understanding their purpose
- Synthesizing feedback from multiple review agents
- Identifying the most critical issues and priorities
- Providing clear, actionable summaries
- Evaluating overall code quality and implementation approach
- Communicating technical feedback in accessible language

Guidelines:
- Provide natural language summaries, not JSON
- Start with the main purpose of the PR
- Highlight critical issues first, then minor ones
- Be constructive and solution-oriented
- Consider the bigger picture and architectural implications
- Balance technical accuracy with readability

Your goal is to provide developers with clear, prioritized feedback they can act on."""

    def process(self, diff_text: str, feedbacks: List[Any]) -> Dict[str, Any]:
        """Process and summarize all review feedback."""
        extracted_summaries = [f.get("summary") if isinstance(f, dict) else str(f) for f in feedbacks if f]

        user_prompt = f"""Analyze the following pull request diff and summarize:
1. What the PR is trying to do (its main purpose).
2. Provide a high-level evaluation: is the implementation clear? any smells?
3. Then summarize the individual review feedbacks below.

Respond with a natural language paragraph.

[DIFF]
{diff_text}

[FEEDBACKS]
{json.dumps(extracted_summaries, indent=2)}"""

        try:
            summary_text = self.invoke(user_prompt).strip()
            return {
                "summary": summary_text,
                "details": extracted_summaries
            }
        except Exception as e:
            return {
                "summary": f"Failed to summarize issues: {str(e)}",
                "details": extracted_summaries
            }
