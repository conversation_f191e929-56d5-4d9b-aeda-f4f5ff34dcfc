"""
Bot Review Assistant - AI-powered code review system.

This package provides specialized agents for code review tasks including:
- Naming convention analysis
- Syntax validation  
- Logic review
- Summary generation
"""

from .pipeline.workflow import ReviewPipeline
from .agents import NamingAgent, SyntaxAgent, LogicAgent, SummaryAgent

__version__ = "1.0.0"
__all__ = [
    'ReviewPipeline',
    'NamingAgent', 'SyntaxAgent', 'LogicAgent', 'SummaryAgent'
]
